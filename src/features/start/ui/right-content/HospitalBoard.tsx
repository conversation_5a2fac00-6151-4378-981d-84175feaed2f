import React, { useState } from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";

import { HospitalBoardEditModal } from "./HospitalBoardEditModal";

const HospitalBoardContainer = styled.div`
  height: 310px;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const Title = styled.h3`
  font-size: 16px;
  font-weight: 500;
  color: #243544;
  margin: 0;
  font-family: "NotoSansJP", sans-serif;
`;

const StyledButton = styled(Button)`
  width: 80px;
  height: 28px;
`;

const ButtonLabel = styled.label`
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: center;
  color: #fff;
`;

const NotificationList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const NotificationItem = styled.div`
  color: #243544;
  font-size: 14px;
  line-height: 1;
  font-family: "NotoSansJP", sans-serif;
`;

export const HospitalBoard = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [boardContent, setBoardContent] = useState(
    "4月21日院長学会のため終日不在\nメジコン錠15mg在庫少、既に発注済み",
  );

  const handleEditClick = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleSave = (content: string) => {
    setBoardContent(content);
    // TODO: Save to backend
  };

  // Split content by newlines to display as separate items
  const contentItems = boardContent
    .split("\n")
    .filter((item) => item.trim() !== "");

  return (
    <>
      <HospitalBoardContainer>
        <Header>
          <Title>院内掲示板</Title>
          {/* <EditButton onClick={handleEditClick}>編集</EditButton> */}
          <StyledButton
            key="all-allow"
            varient="secondary"
            onClick={handleEditClick}
          >
            <ButtonLabel>編集</ButtonLabel>
          </StyledButton>
        </Header>
        <NotificationList>
          {contentItems.map((item, index) => (
            <NotificationItem key={index}>{item}</NotificationItem>
          ))}
        </NotificationList>
      </HospitalBoardContainer>

      <HospitalBoardEditModal
        isOpen={isModalOpen}
        initialContent={boardContent}
        onClose={handleModalClose}
        onSave={handleSave}
      />
    </>
  );
};
