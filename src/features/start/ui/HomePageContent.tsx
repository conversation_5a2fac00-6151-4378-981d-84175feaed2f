import { Layout } from "antd";
import styled from "styled-components";

import { PatientProvider } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";

import { StartPageSidebar } from "./left-content";
import { RightContent } from "./right-content";

const HomePageContentContainer = styled(Layout.Content)`
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: 20px;
  padding: 20px 20px 0px 0px;
`;

export const HomePageContent: React.FC = () => {
  return (
    <PatientProvider>
      <HomePageContentContainer>
        <StartPageSidebar />
        <RightContent />
      </HomePageContentContainer>
    </PatientProvider>
  );
};
